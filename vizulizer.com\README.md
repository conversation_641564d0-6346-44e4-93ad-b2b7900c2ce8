# Vizulizer.com

Professional HTML development tools for modern web developers. A comprehensive suite of HTML processing tools with enterprise-grade precision. All processing is done locally in your browser, ensuring code security and privacy protection.

## 🚀 Features

### Core Tools
- **HTML Beautifier** - Automatically fix HTML syntax errors and format code structure
- **HTML Minifier** - Remove extra whitespace and comments, optimize file size
- **Link Cleaner** - Batch remove or clean links in HTML with multiple options
- **HTML to JS Converter** - Convert HTML code to JavaScript variable format
- **CSS Inliner** - Inline external CSS styles into HTML for better performance

### Enhanced Features
- **Real-time Character Count** - Display character, line, and word counts
- **Drag & Drop Support** - Drag HTML files directly to input area
- **One-click Copy** - Quickly copy processed results
- **File Download** - Download processed files with appropriate extensions
- **Keyboard Shortcuts** - Improve workflow efficiency
- **Responsive Design** - Perfect adaptation to all devices
- **Dark Theme** - Professional dark interface inspired by modern development tools

## 📁 Project Structure

```
ai-tools/
├── index.html          # Main application page
├── about.html           # About page with company info
├── privacy.html         # Privacy policy
├── terms.html           # Terms of service
├── scripts/
│   └── main.js         # Core JavaScript functionality
├── styles/
│   ├── main.css        # Main stylesheet with dark theme
│   ├── about.css       # About page specific styles
│   └── legal.css       # Legal pages stylesheet
└── README.md           # Project documentation
```

## 🎯 使用方法

### 基本使用
1. 打开 `index.html` 文件
2. 选择需要的工具功能（修复美化、压缩等）
3. 在输入框中粘贴或输入HTML代码
4. 点击处理按钮获得结果
5. 复制或下载处理后的代码

### 高级功能
- **拖拽上传**: 直接将HTML文件拖拽到输入框
- **键盘快捷键**:
  - `Ctrl+Enter` / `Cmd+Enter`: 处理HTML
  - `Ctrl+K` / `Cmd+K`: 清空输入
  - `Ctrl+D` / `Cmd+D`: 下载结果

## 🛠️ 技术栈

- **前端**: HTML5, CSS3, JavaScript ES6+
- **样式**: 响应式设计，支持现代浏览器
- **图标**: Font Awesome 6.0
- **特性**: 本地处理，无需服务器

## 🔧 工具详细说明

### 1. HTML修复美化
- 自动缩进格式化
- 修复未闭合标签
- 移除空标签
- 统一代码风格

### 2. HTML压缩
- 移除HTML注释
- 压缩多余空白字符
- 优化内联CSS和JavaScript
- 显示压缩比例

### 3. 清除链接
- 移除href属性
- 清除onclick事件
- 保留链接文本选项
- 批量处理链接

### 4. 转JS变量
- 自定义变量名
- 支持const/var声明
- 自动转义引号
- 生成多行字符串

### 5. 内联CSS
- 处理外部CSS文件
- 移除link标签
- CSS代码压缩
- 样式内联优化

## 🔒 隐私安全

- **本地处理**: 所有代码处理均在浏览器本地完成
- **无数据上传**: 您的代码不会被发送到任何服务器
- **隐私保护**: 完全保护您的代码隐私和安全
- **开源透明**: 代码逻辑完全透明可查

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🎨 界面特色

- **现代化设计**: 简洁美观的用户界面
- **响应式布局**: 完美适配桌面和移动设备
- **直观操作**: 简单易用的操作流程
- **实时反馈**: 即时的操作反馈和状态提示

## 📞 Contact Information

- **Technical Support**: <EMAIL>
- **Bug Reports**: <EMAIL>
- **Business Cooperation**: <EMAIL>

## 📄 License

Copyright © 2025 Vizulizer.com Development Team

This project is licensed under the MIT License. See the legal pages for details.

## 🔄 Changelog

### v2.0.0 (2025-01-31)
- Complete redesign with dark theme
- Professional interface inspired by modern development tools
- Enhanced user experience with improved animations
- Full English localization
- Improved accessibility and responsive design
- Added advanced notification system

### v1.0.0 (2025-01-31)
- Initial release
- All core HTML processing features
- Basic user interface
- Responsive design support

---

**Enjoy efficient HTML processing with professional-grade tools!** 🎉
