/* 全局样式 - 科技感深色主题 */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-glass: rgba(30, 41, 59, 0.6);

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    --border-color: rgba(148, 163, 184, 0.1);
    --border-hover: rgba(59, 130, 246, 0.3);

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    background-image:
        radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.08) 0px, transparent 50%),
        radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.08) 0px, transparent 50%),
        radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.08) 0px, transparent 50%),
        radial-gradient(at 0% 100%, hsla(120, 100%, 50%, 0.05) 0px, transparent 50%);
    min-height: 100vh;
    overflow-x: hidden;
    
}

/* 主题切换 */
body.light-theme {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-glass: rgba(248, 250, 252, 0.9);

    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;

    --border-color: rgba(148, 163, 184, 0.3);
    --border-hover: rgba(59, 130, 246, 0.4);

    background-image:
        radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.05) 0px, transparent 50%),
        radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.05) 0px, transparent 50%),
        radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.05) 0px, transparent 50%);
}

/* 浅色主题下的特殊样式 */
body.light-theme .header {
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid var(--border-color);
}

body.light-theme .nav-link {
    color: var(--text-secondary);
}

body.light-theme .nav-link:hover {
    color: var(--text-primary);
}

body.light-theme .nav-link.active {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

body.light-theme .modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

body.light-theme .modal-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

body.light-theme .modal-close {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

body.light-theme .modal-close:hover {
    background: var(--error-color);
    color: white;
}

body.light-theme textarea {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

body.light-theme textarea:focus {
    border-color: var(--border-hover);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

body.light-theme .options-panel {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

body.light-theme .options-panel label {
    color: var(--text-secondary);
}

body.light-theme .options-panel label:hover {
    color: var(--text-primary);
}

body.light-theme .options-panel input[type="text"] {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

body.light-theme .btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

body.light-theme .btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-hover);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 头部样式 - 科技感导航栏 */
.header {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.header:hover::before {
    opacity: 0.05;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
}

.logo-icon {
    position: relative;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon i {
    font-size: 2.2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: 2;
    position: relative;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.2;
    filter: blur(10px);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.2;
    }

    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.4;
    }
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* PC端导航样式 */
.nav-header {
    display: none;
}

.nav-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* PC端导航链接样式 */
.nav-links .nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    overflow: hidden;
}

.nav-links .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-links .nav-link:hover {
    color: var(--text-primary);
    transform: translateY(-2px);
}

.nav-links .nav-link:hover::before {
    opacity: 0.1;
}

.nav-links .nav-link.active {
    color: var(--text-primary);
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid var(--border-hover);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.nav-links .nav-link i {
    font-size: 1rem;
}

/* PC端隐藏图标，只显示文字 */
@media (min-width: 769px) {
    .nav-links .nav-link i {
        display: none;
    }

    .nav-links .nav-link span {
        margin-left: 0;
    }
}

/* 头部控制按钮 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    position: relative;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 12px;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.theme-toggle:hover {
    color: var(--text-primary);
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.theme-toggle i {
    font-size: 1.2rem;
    z-index: 2;
    position: relative;
}

.toggle-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.theme-toggle:hover .toggle-bg {
    opacity: 0.1;
}

/* 移动端菜单按钮 - 重新设计 */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 12px;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 4px;
    position: relative;
    overflow: hidden;
}

.mobile-menu-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.mobile-menu-toggle span {
    width: 22px;
    height: 3px;
    background: var(--text-secondary);
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    position: relative;
    z-index: 2;
}

.mobile-menu-toggle:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.mobile-menu-toggle:hover::before {
    opacity: 0.1;
}

.mobile-menu-toggle:hover span {
    background: var(--text-primary);
}

.mobile-menu-toggle.active {
    border-color: var(--primary-color);
}

.mobile-menu-toggle.active::before {
    opacity: 0.2;
}

.mobile-menu-toggle.active span {
    background: var(--primary-color);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* 主要内容样式 - 科技感设计 */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

/* Hero区域 - 增强版 */
.hero {
    position: relative;
    text-align: center;
    margin-bottom: 8rem;
    padding: 8rem 0;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-element {
    position: absolute;
    opacity: 0.15;
    animation: float var(--duration) ease-in-out infinite;
    animation-delay: var(--delay);
    pointer-events: none;
}

.code-symbol {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    font-family: 'JetBrains Mono', monospace;
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.floating-element:nth-child(1) {
    top: 15%;
    left: 10%;
    font-size: 3rem;
}

.floating-element:nth-child(2) {
    top: 25%;
    right: 15%;
    font-size: 2.5rem;
}

.floating-element:nth-child(3) {
    bottom: 35%;
    left: 15%;
    font-size: 2rem;
}

.floating-element:nth-child(4) {
    top: 45%;
    right: 25%;
    font-size: 1.8rem;
}

.floating-element:nth-child(5) {
    bottom: 20%;
    left: 30%;
    font-size: 2.2rem;
}

.floating-element:nth-child(6) {
    top: 60%;
    right: 10%;
    font-size: 2.8rem;
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.05;
}

.grid-line {
    position: absolute;
    background: var(--gradient-primary);
    animation: gridAppear 2s ease-out;
    animation-delay: var(--delay);
    animation-fill-mode: both;
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
}

.grid-line.horizontal:nth-child(1) {
    top: 20%;
}

.grid-line.horizontal:nth-child(2) {
    top: 50%;
}

.grid-line.horizontal:nth-child(3) {
    top: 80%;
}

.grid-line.vertical {
    width: 1px;
    height: 100%;
}

.grid-line.vertical:nth-child(4) {
    left: 25%;
}

.grid-line.vertical:nth-child(5) {
    left: 75%;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    left: var(--x);
    top: var(--y);
    animation: particleFloat 6s ease-in-out infinite;
    animation-delay: var(--delay);
    box-shadow: 0 0 10px var(--primary-color);
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }

    25% {
        transform: translateY(-30px) rotate(90deg) scale(1.1);
    }

    50% {
        transform: translateY(-60px) rotate(180deg) scale(0.9);
    }

    75% {
        transform: translateY(-30px) rotate(270deg) scale(1.1);
    }
}

@keyframes gridAppear {
    0% {
        transform: scaleX(0);
        opacity: 0;
    }

    100% {
        transform: scaleX(1);
        opacity: 0.05;
    }
}

@keyframes particleFloat {

    0%,
    100% {
        transform: translateY(0px);
        opacity: 0.8;
    }

    50% {
        transform: translateY(-20px);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 50px;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 2.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hero-badge:hover {
    border-color: var(--border-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.hero-badge i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.badge-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.hero-badge:hover .badge-glow {
    opacity: 0.1;
}

.hero h2 {
    font-size: 4.5rem;
    font-weight: 900;
    margin-bottom: 2.5rem;
    line-height: 1.1;
    letter-spacing: -0.025em;
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero p {
    font-size: 1.4rem;
    color: var(--text-muted);
    max-width: 800px;
    margin: 0 auto 3rem;
    font-weight: 400;
    line-height: 1.7;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.feature-highlight:hover {
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.feature-highlight i {
    color: var(--primary-color);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.stat-item:hover {
    border-color: var(--border-hover);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-item:hover::before {
    opacity: 0.05;
}

.stat-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.95rem;
    color: var(--text-muted);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 4rem;
}

.btn-hero {
    position: relative;
    padding: 1.5rem 3rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 16px;
    overflow: hidden;
    min-width: 200px;
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover .btn-shine {
    left: 100%;
}

.hero-scroll-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.9rem;
    animation: bounce 2s ease-in-out infinite;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.scroll-arrow:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* 功能概览区域 */
.features-overview {
    margin-bottom: 6rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h3 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.feature-card:hover::before {
    opacity: 0.05;
}

.feature-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.feature-icon i {
    font-size: 2.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: 2;
    position: relative;
}

.icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    filter: blur(15px);
    transition: all 0.3s ease;
}

.feature-card:hover .icon-glow {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.2);
}

.feature-card h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-stats {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.feature-stats span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.feature-stats i {
    color: var(--primary-color);
}

/* How It Works Section */
.how-it-works {
    margin-bottom: 8rem;
    position: relative;
}

.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.step-item {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0 auto 2rem;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.3;
    filter: blur(10px);
    z-index: -1;
}

.step-content {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.step-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.step-content:hover {
    transform: translateY(-5px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-xl);
}

.step-content:hover::before {
    opacity: 0.05;
}

.step-icon {
    width: 50px;
    height: 50px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.step-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.step-connector {
    width: 50px;
    height: 2px;
    background: var(--gradient-primary);
    position: relative;
    opacity: 0.3;
}

.step-connector::before {
    content: '';
    position: absolute;
    right: -5px;
    top: -3px;
    width: 0;
    height: 0;
    border-left: 8px solid var(--primary-color);
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
}

/* Why Choose Us Section */
.why-choose-us {
    margin-bottom: 8rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.benefit-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.benefit-card:hover::before {
    opacity: 0.05;
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.benefit-card h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.benefit-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.benefit-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--primary-color);
    font-size: 0.85rem;
    font-weight: 600;
}

/* 工具选择区域 */
.tools-section {
    margin-bottom: 6rem;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.tool-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.tool-card:hover::before {
    opacity: 0.08;
}

.tool-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-lg);
}

.tool-icon i {
    font-size: 2.5rem;
    color: white;
}

.tool-card h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.tool-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 1rem 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.tool-btn i {
    transition: transform 0.3s ease;
}

.tool-btn:hover i {
    transform: translateX(4px);
}

/* 工具弹窗模态框 */
.tool-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding: 2.5vh 2rem;
    overflow-y: auto;
    box-sizing: border-box;
}

.tool-modal.active {
    display: flex;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 95vh;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 24px;
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    animation: slideUp 0.3s ease;
    margin: 0 auto;
    flex-shrink: 0;
    /* 确保弹窗不会超出屏幕 */
    min-height: 0;
    display: flex;
    flex-direction: column;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 2.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    flex-shrink: 0;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.modal-body {
    padding: 2rem 2.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* 模态框内的样式 */
.modal-body .input-section,
.modal-body .output-section {
    margin-bottom: 1.5rem;
}

.modal-body .input-header,
.modal-body .output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modal-body .input-header h4,
.modal-body .output-header h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body .input-actions,
.modal-body .output-actions {
    display: flex;
    gap: 0.75rem;
}

.modal-body textarea {
    width: 100%;
    height: 200px;
    padding: 1.25rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
}

.modal-body textarea:focus {
    outline: none;
    border-color: var(--border-hover);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-body textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.modal-body textarea:read-only {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

.modal-body .process-section {
    text-align: center;
    margin: 2.5rem 0;
}

.modal-body .tool-options {
    border-top: 1px solid var(--border-color);
    padding-top: 2.5rem;
    margin-top: 2rem;
}

.modal-body .options-panel {
    display: none;
    background: var(--bg-glass);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.modal-body .options-panel.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.modal-body .options-panel h4 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body .options-panel label {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.3s ease;
}

.modal-body .options-panel label:hover {
    color: var(--text-primary);
}

.modal-body .options-panel input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.modal-body .options-panel input[type="text"] {
    margin-left: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.modal-body .options-panel input[type="text"]:focus {
    outline: none;
    border-color: var(--border-hover);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 工具弹窗模态框 */
.tool-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 999999 !important;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding: 2.5vh 2rem;
    overflow-y: auto;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}

.tool-modal.active {
    display: flex;
}

/* 防止模态框打开时页面滚动 - 仅移动端 */
@media (max-width: 768px) {
    body.modal-open {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        height: 100% !important;
    }
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
    z-index: 999998;
}

.modal-content {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 24px;
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    animation: slideUp 0.3s ease;
    z-index: 999999;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2rem 2.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--error-color);
    color: white;
    transform: scale(1.1);
}

.modal-body {
    padding: 2.5rem;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

/* 模态框内的输入输出区域 */
.modal-body .input-section,
.modal-body .output-section {
    margin-bottom: 2.5rem;
}

.modal-body .input-header,
.modal-body .output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.modal-body .input-header h4,
.modal-body .output-header h4 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body .input-actions,
.modal-body .output-actions {
    display: flex;
    gap: 0.75rem;
}

.modal-body textarea {
    width: 100%;
    height: 200px;
    padding: 1.25rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
}

.modal-body textarea:focus {
    outline: none;
    border-color: var(--border-hover);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-body textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.modal-body textarea:read-only {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

.modal-body .process-section {
    text-align: center;
    margin: 2.5rem 0;
}

/* 工具选项面板 */
.modal-body .tool-options {
    border-top: 1px solid var(--border-color);
    padding-top: 2.5rem;
    margin-top: 2rem;
}

.modal-body .options-panel {
    display: none;
    background: var(--bg-glass);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.modal-body .options-panel.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.modal-body .options-panel h4 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body .options-panel label {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.3s ease;
}

.modal-body .options-panel label:hover {
    color: var(--text-primary);
}

.modal-body .options-panel input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.modal-body .options-panel input[type="text"] {
    margin-left: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 150px;
    transition: all 0.3s ease;
}

.modal-body .options-panel input[type="text"]:focus {
    outline: none;
    border-color: var(--border-hover);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 工具区域样式 - 深色卡片设计 */
.tools {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 16px;
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.3),
        0 10px 10px -5px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.tool-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    background: rgba(15, 23, 42, 0.8);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
    gap: 1px;
}

.tab-btn {
    padding: 1.25rem 1rem;
    border: none;
    background: rgba(30, 41, 59, 0.5);
    color: #cbd5e1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #f8fafc;
    transform: translateY(-1px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.tab-btn i {
    font-size: 1.1rem;
}

.tool-content {
    padding: 2.5rem;
}

.input-section,
.output-section {
    margin-bottom: 2.5rem;
}

.input-header,
.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.input-header h3,
.output-header h3 {
    color: #f8fafc;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-header h3::before {
    content: "📝";
    font-size: 1.1rem;
}

.output-header h3::before {
    content: "✨";
    font-size: 1.1rem;
}

.input-actions,
.output-actions {
    display: flex;
    gap: 0.75rem;
}

textarea {
    width: 100%;
    height: 250px;
    padding: 1.25rem;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    color: #e2e8f0;
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

textarea:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(15, 23, 42, 0.9);
}

textarea::placeholder {
    color: #64748b;
    font-style: italic;
}

textarea:read-only {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(148, 163, 184, 0.15);
}

.process-section {
    text-align: center;
    margin: 3rem 0;
}

/* 按钮样式 - 科技感设计 */
.btn {
    padding: 0.875rem 1.75rem;
    border: 1px solid transparent;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    background-clip: padding-box;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    font-size: 1.125rem;
    padding: 1.25rem 2.5rem;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
    border-color: rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-glass);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary:hover::before {
    opacity: 0.1;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 8px;
}

/* 特殊按钮效果 */
.btn-glow {
    position: relative;
}

.btn-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: inherit;
    opacity: 0;
    filter: blur(20px);
    transition: opacity 0.3s ease;
    z-index: -2;
}

.btn-glow:hover::after {
    opacity: 0.6;
}

/* 工具选项样式 - 深色卡片风格 */
.tool-options {
    border-top: 1px solid rgba(148, 163, 184, 0.1);
    padding-top: 2.5rem;
    margin-top: 2rem;
}

.options-panel {
    display: none;
    background: rgba(15, 23, 42, 0.6);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid rgba(148, 163, 184, 0.1);
}

.options-panel.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.options-panel h4 {
    margin-bottom: 1.5rem;
    color: #f8fafc;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.options-panel h4::before {
    content: "⚙️";
    font-size: 1rem;
}

.options-panel label {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    cursor: pointer;
    color: #cbd5e1;
    font-weight: 500;
    transition: color 0.3s ease;
}

.options-panel label:hover {
    color: #f8fafc;
}

.options-panel input[type="checkbox"] {
    margin-right: 0.75rem;
    width: 18px;
    height: 18px;
    accent-color: #3b82f6;
}

.options-panel input[type="text"] {
    margin-left: 0.75rem;
    padding: 0.5rem 0.75rem;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 8px;
    color: #e2e8f0;
    font-size: 0.9rem;
    min-width: 150px;
}

.options-panel input[type="text"]:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 页脚样式 - 科技感设计 */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    margin-top: 6rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.02;
    z-index: -1;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 4rem;
    padding: 4rem 0 2rem;
    align-items: start;
}

.footer-brand {
    max-width: 500px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo .logo-icon {
    width: 40px;
    height: 40px;
}

.footer-logo .logo-icon i {
    font-size: 1.8rem;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-brand p {
    color: var(--text-muted);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.social-link:hover {
    color: white;
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.social-link:hover::before {
    opacity: 1;
}

.footer-links-section h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    border-radius: 6px;
}

.footer-links a:hover {
    color: var(--text-primary);
    padding-left: 0.5rem;
}

.footer-divider {
    height: 1px;
    background: var(--border-color);
    margin: 2rem 0;
}

.footer-bottom {
    text-align: center;
    padding-bottom: 2rem;
}

.footer-bottom p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* 额外的科技感动画效果 */
@keyframes matrix {
    0% {
        transform: translateY(-100vh);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

@keyframes glow {

    0%,
    100% {
        box-shadow: 0 0 5px var(--primary-color);
    }

    50% {
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        transform: translateY(30px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes rotateIn {
    0% {
        transform: rotate(-180deg) scale(0.5);
        opacity: 0;
    }

    100% {
        transform: rotate(0deg) scale(1);
        opacity: 1;
    }
}

/* 鼠标悬停时的粒子效果 */
.particle-effect {
    position: relative;
    overflow: hidden;
}

.particle-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(59, 130, 246, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.particle-effect:hover::before {
    opacity: 1;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 进度条动画 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* 代码高亮效果 */
.code-highlight {
    position: relative;
}

.code-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.code-highlight:hover::before {
    transform: translateX(100%);
}

/* 全屏加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
}

.loader-logo {
    font-size: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s ease-in-out infinite;
}

.loader-text {
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    animation: fadeInUp 1s ease-out 0.5s both;
}

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--bg-tertiary);
    z-index: 1001;
}

.scroll-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.1s ease;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.visible {
    transform: translateY(0);
    opacity: 1;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* 页面特定样式 */
.page-hero {
    position: relative;
    text-align: center;
    margin-bottom: 6rem;
    padding: 6rem 0;
    overflow: hidden;
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.page-hero .hero-content h1 {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 2rem;
    line-height: 1.1;
    letter-spacing: -0.025em;
}

.values-section,
.stats-section,
.tech-section,
.contact-section {
    margin-bottom: 6rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.value-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.value-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.value-card:hover::before {
    opacity: 0.05;
}

.value-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.value-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    opacity: 0.05;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tech-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.tech-card:hover {
    transform: translateY(-5px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-xl);
}

.tech-card:hover::before {
    opacity: 0.05;
}

.tech-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.tech-card h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.tech-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.contact-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.contact-card:hover::before {
    opacity: 0.05;
}

.contact-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.contact-card h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.contact-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-link:hover {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Contact & Company Section - New Design */
.contact-company-section {
    margin-bottom: 6rem;
}

.contact-methods {
    margin-bottom: 5rem;
}

.contact-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.contact-method {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.contact-method::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.contact-method:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: var(--border-hover);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.contact-method:hover::before {
    opacity: 0.05;
}

.method-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.method-content {
    flex: 1;
}

.method-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.method-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.contact-email {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.contact-email:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.company-info-wrapper {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 24px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.company-info-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.02;
    z-index: -1;
}

.company-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--border-color);
}

.company-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
}

.company-title h3 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-title p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

.company-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.detail-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.detail-section:hover {
    transform: translateY(-3px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-lg);
}

.detail-section h4 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-section h4 i {
    color: var(--primary-color);
    font-size: 1rem;
}

.detail-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem 0;
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 100px;
    flex-shrink: 0;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
    flex: 1;
}

.copyright-notice {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 2.5rem;
    margin-top: 2rem;
}

.copyright-content {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.copyright-symbol {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.copyright-text {
    flex: 1;
}

.copyright-text p {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.copyright-text p:first-child {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* About页面特有样式 */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-marker {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 0 0 4px var(--bg-primary), 0 0 20px rgba(59, 130, 246, 0.3);
    z-index: 2;
}

.timeline-content {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    width: calc(50% - 40px);
    position: relative;
    transition: all 0.3s ease;
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    transform: translateY(-50%);
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: auto;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    left: -20px;
    border-right-color: var(--border-color);
}

.timeline-item:nth-child(even) .timeline-content::before {
    right: -20px;
    border-left-color: var(--border-color);
}

.timeline-content:hover {
    transform: translateY(-5px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-xl);
}

.timeline-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.timeline-date {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--gradient-primary);
    color: white;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.stat-description {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
    font-style: italic;
}

/* 移动端时间线样式 */
@media (max-width: 768px) {
    .timeline::before {
        left: 30px;
    }

    .logo-icon {
        width: 25px;
        height: 25px;

    }

    .nav-header {
        width: 100%;
    }

    .stat-item {
        padding: 1rem 0.6rem;
    }

    .stat-icon {
        width: 35px;
        height: 35px;
    }

    .stat-number {
        font-size: 0.95rem !important;
    }

    .feature-highlight {
        justify-content: center;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .timeline-item {
        flex-direction: row !important;
        margin-bottom: 2rem;
    }

    .timeline-marker {
        left: 30px;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .timeline-content {
        width: calc(100% - 80px);
        margin-left: 80px !important;
    }

    .timeline-content::before {
        left: -20px !important;
        right: auto !important;
        border-right-color: var(--border-color) !important;
        border-left-color: transparent !important;
    }
}

/* Privacy页面特有样式 */
.privacy-highlights {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.highlight-item:hover {
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.highlight-item i {
    color: var(--success-color);
    font-size: 1.1rem;
}

.principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.principle-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.principle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.principle-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.principle-card:hover::before {
    opacity: 0.05;
}

.principle-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.no-collect-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.no-collect-item {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.no-collect-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.no-collect-item:hover {
    transform: translateY(-5px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-xl);
}

.no-collect-item:hover::before {
    opacity: 0.03;
}

.no-collect-icon {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    color: var(--text-secondary);
    font-size: 1.5rem;
}

.no-icon {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 1.2rem;
    background: var(--bg-primary);
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--error-color);
}

.technical-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.technical-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.technical-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.technical-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.technical-card:hover::before {
    opacity: 0.05;
}

.technical-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.tech-details {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.tech-tag {
    padding: 0.25rem 0.75rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-option {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.contact-option:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-2xl);
}

.contact-option:hover::before {
    opacity: 0.05;
}

/* Privacy页面新样式 - 非卡片式 */
.privacy-content {
    max-width: 900px;
    margin: 0 auto;
}

.content-wrapper {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-xl);
}

.privacy-intro {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.privacy-intro h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.last-updated {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.intro-text {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.7;
    max-width: 700px;
    margin: 0 auto;
}

.privacy-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.privacy-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.privacy-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.privacy-section h3 i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.privacy-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 1.5rem 0 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.privacy-section h4 i {
    color: var(--primary-color);
}

.section-content {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1rem;
}

.section-content p {
    margin-bottom: 1.5rem;
}

.privacy-list,
.rights-list,
.security-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.privacy-list li,
.rights-list li,
.security-list li {
    padding: 0.75rem 0;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.05);
}

.privacy-list li:last-child,
.rights-list li:last-child,
.security-list li:last-child {
    border-bottom: none;
}

.privacy-list li i {
    color: var(--error-color);
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.text-error {
    color: var(--error-color);
}

.tech-explanation,
.storage-info,
.third-party-info {
    margin: 2rem 0;
}

.tech-item,
.storage-item,
.service-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.tech-item:hover,
.storage-item:hover,
.service-item:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.tech-item h4,
.storage-item h4,
.service-item h4 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
}

.tech-item p,
.storage-item p,
.service-item p {
    margin: 0;
    color: var(--text-secondary);
}

.contact-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.contact-info p {
    margin: 0.5rem 0;
}

.contact-info a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.contact-info a:hover {
    text-decoration: underline;
}

.response-time {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 1rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 2rem 1.5rem;
        margin: 0 1rem;
        border-radius: 16px;
    }

    .privacy-intro h2 {
        font-size: 2rem;
    }

    .intro-text {
        font-size: 1.1rem;
    }

    .privacy-section h3 {
        font-size: 1.3rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .tech-item,
    .storage-item,
    .service-item {
        padding: 1rem;
    }
}

/* Terms页面样式 */
.terms-highlights {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.terms-content {
    max-width: 900px;
    margin: 0 auto;
}

.terms-intro {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.terms-intro h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.terms-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.terms-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.service-list {
    margin: 2rem 0;
}

.service-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.service-item:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.service-item h4 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-item h4 i {
    color: var(--primary-color);
}

.service-item p {
    margin: 0;
    color: var(--text-secondary);
}

.responsibility-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.responsibility-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(148, 163, 184, 0.05);
}

.responsibility-list li:last-child {
    border-bottom: none;
}

.warning-box,
.info-box {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    border-left: 4px solid var(--warning-color);
}

.info-box {
    border-left-color: var(--info-color);
}

.warning-box h4,
.info-box h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.warning-box h4 i {
    color: var(--warning-color);
}

.info-box h4 i {
    color: var(--info-color);
}

.warning-box p,
.info-box p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

.warning-box ul,
.info-box ul {
    margin: 1rem 0 0 0;
    padding-left: 1.5rem;
}

.warning-box li,
.info-box li {
    margin: 0.5rem 0;
    color: var(--text-secondary);
}

/* 移动端Terms页面优化 */
@media (max-width: 768px) {
    .terms-highlights {
        gap: 1rem;
    }

    .terms-intro h2 {
        font-size: 2rem;
    }

    .service-item {
        padding: 1rem;
    }

    .warning-box,
    .info-box {
        padding: 1rem;
    }

    /* Contact & Company Mobile Styles */
    .contact-row {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1.5rem;
        gap: 1rem;
    }

    .contact-method:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .method-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin: 0 auto;
    }

    .method-content h4 {
        font-size: 1.2rem;
    }

    .company-info-wrapper {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .company-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .company-logo {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .company-title h3 {
        font-size: 1.8rem;
    }

    .company-details-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .detail-section {
        padding: 1.5rem;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .detail-value {
        text-align: left;
    }

    .copyright-notice {
        padding: 2rem 1.5rem;
    }

    .copyright-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .copyright-symbol {
        margin: 0 auto;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-stats {
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 320px;
        height: 100vh;
        background: var(--bg-secondary);
        backdrop-filter: blur(20px);
        border-right: 1px solid var(--border-color);
        flex-direction: column;
        gap: 0;
        padding: 0;
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        box-shadow: var(--shadow-2xl);
        z-index: 1002;
        overflow-y: auto;
    }

    .nav-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--border-color);
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
    }

    .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--text-primary);
        font-weight: 700;
        font-size: 1.1rem;
    }

    .nav-logo i {
        color: var(--primary-color);
        font-size: 1.3rem;
    }

    .nav-close {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 10px;
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .nav-close:hover {
        background: var(--error-color);
        color: white;
        transform: scale(1.1);
    }

    .nav-links {
        padding: 2rem 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0;
    }

    .nav.active {
        left: 0;
    }

    .nav::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--gradient-primary);
        opacity: 0.03;
        z-index: -1;
    }

    .nav-links .nav-link {
        padding: 1.5rem 2rem;
        border-radius: 12px;
        margin: 0 1rem 0.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        border: 1px solid transparent;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-primary);
        opacity: 0.1;
        transition: left 0.3s ease;
        z-index: -1;
    }

    .nav-link:hover::before,
    .nav-link.active::before {
        left: 0;
    }

    .nav-link:last-child {
        border-bottom: 1px solid var(--border-color);
    }

    .nav-link span {
        margin-left: 0.75rem;
        font-weight: 600;
    }

    .nav-link i {
        font-size: 1.2rem;
        width: 24px;
        text-align: center;
    }

    /* 移动端导航遮罩 */
    .nav-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }

    .nav-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .hero h2 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn-hero {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        padding: 1.25rem 2rem;
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .steps-container {
        flex-direction: column;
        gap: 2rem;
    }

    .step-connector {
        width: 2px;
        height: 50px;
        transform: rotate(90deg);
    }

    .step-connector::before {
        right: -3px;
        top: 45px;
        transform: rotate(90deg);
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .modal-content {
        margin: 0;
        max-height: 100vh;
        border-radius: 0;
        width: 100%;
        max-width: 100%;
        display: flex;
        flex-direction: column;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        transform: none !important;
        z-index: 999999 !important;
    }

    .modal-header {
        padding: 1rem 1.5rem;
        position: sticky;
        top: 0;
        z-index: 999999;
        flex-shrink: 0;
    }

    .modal-body {
        padding: 1rem 1.5rem 2rem;
        flex: 1;
        overflow-y: auto;
        max-height: calc(100vh - 80px);
        -webkit-overflow-scrolling: touch;
    }

    .modal-body .input-section,
    .modal-body .output-section {
        margin-bottom: 1.5rem;
    }

    .modal-body .input-header,
    .modal-body .output-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .modal-body .input-header h4,
    .modal-body .output-header h4 {
        font-size: 1rem;
    }

    /* 移动端工具界面优化 */
    .modal-body textarea {
        min-height: 200px;
        font-size: 14px;
        line-height: 1.5;
    }

    .modal-body .tool-controls {
        flex-direction: column;
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .modal-body .btn {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
        justify-content: center;
    }

    .modal-body .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .modal-body .btn-group .btn {
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .modal-body .btn-group .btn:last-child {
        margin-bottom: 0;
    }

    /* 移动端统计信息优化 */
    .modal-body .stats-row {
        flex-direction: column;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .modal-body .stats-item {
        text-align: center;
        padding: 0.75rem;
        background: rgba(15, 23, 42, 0.5);
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    /* 移动端文件操作按钮 */
    .modal-body .file-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .modal-body .file-actions .btn {
        width: 100%;
        padding: 0.875rem;
    }

    .modal-body .input-actions,
    .modal-body .output-actions {
        width: 100%;
        justify-content: flex-start;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .modal-body .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        flex: 1;
        min-width: 80px;
    }

    .modal-body textarea {
        height: 120px;
        font-size: 13px;
        padding: 1rem;
        line-height: 1.4;
    }

    .modal-body .process-section {
        margin: 1.5rem 0;
        text-align: center;
    }

    .modal-body .btn-primary {
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
    }

    .modal-body .tool-options {
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }

    .modal-body .options-panel {
        padding: 1rem;
        border-radius: 12px;
    }

    .modal-body .options-panel h4 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .modal-body .options-panel label {
        margin-bottom: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .modal-body .options-panel input[type="text"] {
        margin-left: 0;
        width: 100%;
        min-width: auto;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .modal-body .options-panel input[type="checkbox"] {
        width: 16px;
        height: 16px;
        margin-right: 0.5rem;
    }

    .modal-body .char-count {
        flex-direction: column;
        gap: 0.5rem;
        font-size: 0.8rem;
        padding: 0.5rem 0;
    }

    .modal-body .char-count span {
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
    }

    .footer-content {
        padding: 3rem 0 1.5rem;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 16px;
    }

    .page-loader {
        /* 移动端加载器优化 */
        background: var(--bg-primary);
        padding: 1rem;
    }

    .loader-content {
        max-width: 280px;
        margin: 0 auto;
    }

    .loader-logo {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .loader-text {
        font-size: 0.9rem;
        margin-top: 0.75rem;
        line-height: 1.4;
        padding: 0 1rem;
    }

    .tool-modal {
        width: 50%;
        max-height: 95vh;
        padding: 1vh 1rem;
        z-index: 999999 !important;
    }

    .modal-content {
        margin: 0 auto;
        max-height: 98vh;
        overflow-y: auto;
        z-index: 999999 !important;
    }

    /* 中等屏幕弹窗优化 */
    .modal-header {
        padding: 1.25rem 2rem;
    }

    .modal-body {
        padding: 1.5rem 2rem;
        max-height: calc(95vh - 80px);
    }

    .modal-body .input-section,
    .modal-body .output-section {
        margin-bottom: 1.25rem;
    }

    .modal-body textarea {
        min-height: 200px;
    }

    .hero {
        padding: 4rem 0;
        min-height: auto;
    }

    .hero h2 {
        font-size: 2.5rem;
    }

    .hero-badge {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        padding: 1.5rem 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .feature-card,
    .tool-card {
        padding: 1.5rem;
    }

    .modal-content {
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
        margin: 0;
        width: 100%;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        transform: none !important;
        z-index: 999999 !important;
    }

    /* 小屏幕工具界面优化 */
    .modal-body {
        padding: 1rem;
    }

    .modal-body textarea {
        min-height: 180px;
        font-size: 13px;
        padding: 0.75rem;
    }

    .modal-body .input-header h4,
    .modal-body .output-header h4 {
        font-size: 0.95rem;
    }

    .modal-body .btn {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    .modal-body .stats-item {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    /* 工具卡片小屏幕优化 */
    .tool-card h4 {
        font-size: 1.2rem;
    }

    .tool-card p {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    .tool-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1.5rem;
    }

    .tool-icon i {
        font-size: 1.5rem;
    }

    .modal-header {
        padding: 1rem;
        border-radius: 0;
        z-index: 999999 !important;
    }

    .modal-body {
        padding: 1rem;
        max-height: calc(100vh - 70px);
    }

    .modal-body textarea {
        height: 120px;
        font-size: 13px;
    }

    .modal-body .btn {
        width: 100%;
        justify-content: center;
    }

    .modal-body .input-actions,
    .modal-body .output-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 2rem 0 1rem;
        text-align: center;
    }

    .footer-brand {
        text-align: center;
    }

    .footer-links-section {
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    /* 移动端导航调整 */
    .nav {
        width: 100%;
        right: -100%;
    }

    .nav.active {
        right: 0;
    }

    /* 移动端触摸优化 */
    .modal-body .btn,
    .tool-card,
    .nav-link {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }

    /* 移动端滚动优化 */
    .modal-body {
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
    }

    .modal-body textarea {
        -webkit-overflow-scrolling: touch;
        resize: vertical;
    }

    /* 移动端间距优化 */
    .modal-body .input-section,
    .modal-body .output-section {
        margin-bottom: 1rem;
    }

    .modal-close {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    /* 移动端工具控制按钮优化 */
    .modal-body .tool-controls {
        flex-direction: column;
        gap: 0.75rem;
        margin: 1rem 0;
    }

    .modal-body .btn-group {
        flex-direction: column;
        width: 100%;
        gap: 0.5rem;
    }

    .modal-body .btn-group .btn {
        border-radius: 8px;
        margin: 0;
    }

    /* 移动端文件操作优化 */
    .modal-body .file-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-body .file-actions .btn {
        width: 100%;
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* 通知样式 - 科技感设计 */
.notification {
    position: fixed;
    top: 24px;
    right: 24px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-2xl);
    padding: 1.25rem 1.75rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 3000;
    min-width: 320px;
    color: var(--text-primary);
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.05;
    z-index: -1;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-info {
    border-left: 4px solid var(--accent-color);
}

.notification i {
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.notification-success i {
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
}

.notification-warning i {
    color: var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
}

.notification-error i {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.notification-info i {
    color: var(--accent-color);
    background: rgba(6, 182, 212, 0.1);
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    margin-left: auto;
    padding: 0.25rem;
    border-radius: 3px;
    transition: background-color 0.3s;
}

/* 移动端通知优化 */
@media (max-width: 480px) {
    .notification {
        top: 16px;
        right: 16px;
        left: 16px;
        min-width: auto;
        padding: 1rem;
        font-size: 0.9rem;
    }

    .notification i {
        font-size: 1rem;
        padding: 0.4rem;
    }
}

/* 键盘快捷键提示 */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    z-index: 1000;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.keyboard-shortcuts.show {
    opacity: 1;
    transform: translateY(0);
}

.keyboard-shortcuts h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.keyboard-shortcuts div {
    margin-bottom: 0.25rem;
}

.keyboard-shortcuts kbd {
    background: #495057;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
}

/* 移动端隐藏键盘快捷键 */
@media (max-width: 768px) {
    .keyboard-shortcuts {
        display: none;
    }

    /* 平板端加载器优化 */
    .page-loader {
        padding: 2rem 1rem;
    }

    .loader-content {
        max-width: 320px;
    }

    .loader-logo {
        font-size: 3rem;
    }

    .loader-text {
        font-size: 1rem;
        margin-top: 1rem;
    }
}

/* 超小屏幕加载器优化 */
@media (max-width: 360px) {
    .page-loader {
        padding: 1rem 0.5rem;
    }

    .loader-content {
        max-width: 240px;
    }

    .loader-logo {
        font-size: 2rem;
        margin-bottom: 0.25rem;
    }

    .loader-text {
        font-size: 0.8rem;
        margin-top: 0.5rem;
        padding: 0 0.5rem;
    }
}

/* 移动端加载动画优化 */
@media (max-width: 480px) {
    /* 减少动画强度以提升性能 */
    .loader-logo {
        animation: pulse 2.5s ease-in-out infinite;
    }

    .loader-text {
        animation: fadeInUp 1.2s ease-out 0.6s both;
    }

    /* 移动端特定的加载提示 */
    .loader-text::after {
        content: "\A正在为您优化移动体验...";
        white-space: pre;
        display: block;
        font-size: 0.75em;
        opacity: 0.7;
        margin-top: 0.5rem;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .page-loader {
        padding: 1rem;
    }

    .loader-content {
        max-width: 400px;
        display: flex;
        align-items: center;
        gap: 2rem;
        text-align: left;
    }

    .loader-logo {
        font-size: 2.5rem;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .loader-text {
        margin-top: 0;
        font-size: 0.9rem;
    }

    .loader-text::after {
        display: none;
    }
}

.notification-close:hover {
    background-color: #f8f9fa;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 字符统计样式 - 科技感设计 */
.char-count {
    display: flex;
    gap: 1.5rem;
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: 0.75rem;
    padding: 0.75rem 0;
    border-top: 1px solid var(--border-color);
    font-weight: 500;
}

.char-count span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-glass);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.char-count span::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.char-count span:hover {
    border-color: var(--border-hover);
    color: var(--text-primary);
}

.char-count span:hover::before {
    opacity: 0.1;
}

/* 拖拽样式 - 科技感设计 */
textarea.drag-over {
    border-color: var(--border-hover) !important;
    background: rgba(59, 130, 246, 0.1) !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.3);
}

/* 键盘快捷键提示 */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.keyboard-shortcuts.show {
    opacity: 1;
}

.keyboard-shortcuts h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
}

.keyboard-shortcuts div {
    margin-bottom: 0.25rem;
}

.keyboard-shortcuts kbd {
    background: #495057;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.7rem;
}