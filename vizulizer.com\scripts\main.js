// 全局变量
let currentTool = 'beautify';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 隐藏页面加载器
    setTimeout(() => {
        const pageLoader = document.getElementById('pageLoader');
        if (pageLoader) {
            pageLoader.classList.add('hidden');
        }
    }, 1000);

    initializeThemeToggle();
    initializeMobileMenu();
    initializeToolCards();
    initializeDragAndDrop();
    initializeAnimations();
    initializeScrollFeatures();
    initializeParticleEffects();

    // 添加输入监听
    const inputTextarea = document.getElementById('inputHtml');
    const outputTextarea = document.getElementById('outputHtml');

    if (inputTextarea && outputTextarea) {
        inputTextarea.addEventListener('input', updateCharacterCount);
        outputTextarea.addEventListener('input', updateCharacterCount);
        updateCharacterCount();
    }
}

// 主题切换功能
function initializeThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    const body = document.body;

    // 检查本地存储的主题设置
    const savedTheme = localStorage.getItem('theme') || 'dark';
    if (savedTheme === 'light') {
        body.classList.add('light-theme');
        themeIcon.className = 'fas fa-moon';
    }

    themeToggle.addEventListener('click', function() {
        body.classList.toggle('light-theme');
        const isLight = body.classList.contains('light-theme');

        themeIcon.className = isLight ? 'fas fa-moon' : 'fas fa-sun';
        localStorage.setItem('theme', isLight ? 'light' : 'dark');

        // 添加切换动画
        themeToggle.style.transform = 'scale(0.9)';
        setTimeout(() => {
            themeToggle.style.transform = 'scale(1)';
        }, 150);
    });
}

// 移动端菜单功能 - 重新设计
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const nav = document.getElementById('nav');
    const navOverlay = document.getElementById('navOverlay');
    const navClose = document.getElementById('navClose');

    function toggleMenu() {
        const isActive = nav.classList.contains('active');

        if (isActive) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        nav.classList.add('active');
        mobileMenuToggle.classList.add('active');
        navOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // 添加动画延迟
        const navLinks = nav.querySelectorAll('.nav-link');
        navLinks.forEach((link, index) => {
            link.style.animationDelay = `${index * 0.1}s`;
            link.style.animation = 'slideInFromLeft 0.4s ease forwards';
        });
    }

    function closeMenu() {
        nav.classList.remove('active');
        mobileMenuToggle.classList.remove('active');
        navOverlay.classList.remove('active');
        document.body.style.overflow = 'auto';

        // 清除动画
        const navLinks = nav.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.style.animation = '';
            link.style.animationDelay = '';
        });
    }

    mobileMenuToggle.addEventListener('click', toggleMenu);
    navOverlay.addEventListener('click', closeMenu);
    navClose.addEventListener('click', closeMenu);

    // 点击导航链接时关闭菜单
    const navLinks = nav.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', closeMenu);
    });

    // ESC键关闭菜单
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && nav.classList.contains('active')) {
            closeMenu();
        }
    });

    // 窗口大小改变时关闭菜单
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && nav.classList.contains('active')) {
            closeMenu();
        }
    });
}

// 工具卡片功能
function initializeToolCards() {
    const toolCards = document.querySelectorAll('.tool-card');
    const featureCards = document.querySelectorAll('.feature-card');

    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const tool = this.dataset.tool;
            openToolModal(tool);
        });
    });

    featureCards.forEach(card => {
        card.addEventListener('click', function() {
            const tool = this.dataset.tool;
            if (tool) {
                openToolModal(tool);
            }
        });
    });
}

// 打开工具模态框
function openToolModal(tool) {
    const modal = document.getElementById('toolModal');
    const modalTitle = document.getElementById('modalTitle');

    currentTool = tool;

    const toolNames = {
        'beautify': 'HTML Beautifier',
        'compress': 'HTML Minifier',
        'clean-links': 'Link Cleaner',
        'to-js': 'HTML to JS Converter',
        'inline-css': 'CSS Inliner'
    };

    modalTitle.textContent = toolNames[tool];
    updateProcessButton();
    updateOptionsPanel();

    modal.classList.add('active');

    // 只在移动端添加modal-open类
    if (window.innerWidth <= 768) {
        document.body.classList.add('modal-open');
    }
    document.body.style.overflow = 'hidden';

    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('inputHtml').focus();
    }, 300);
}

// 关闭工具模态框
function closeToolModal() {
    const modal = document.getElementById('toolModal');
    modal.classList.remove('active');

    // 移除modal-open类（如果存在）
    document.body.classList.remove('modal-open');
    document.body.style.overflow = 'auto';
}

// 更新选项面板
function updateOptionsPanel() {
    const panels = document.querySelectorAll('.options-panel');
    panels.forEach(panel => panel.classList.remove('active'));

    const activePanel = document.getElementById(currentTool + '-options');
    if (activePanel) {
        activePanel.classList.add('active');
    }
}

// 初始化动画
function initializeAnimations() {
    // 数字动画
    animateNumbers();

    // 滚动动画
    initializeScrollAnimations();

    // 视差效果
    initializeParallax();
}

// 数字动画
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.dataset.target);
                animateNumber(entry.target, target);
                observer.unobserve(entry.target);
            }
        });
    });

    statNumbers.forEach(number => observer.observe(number));
}

function animateNumber(element, target) {
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 30);
}

// 滚动动画
function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('.feature-card, .tool-card, .hero-content');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });

    animatedElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
}

// 视差效果
function initializeParallax() {
    const floatingElements = document.querySelectorAll('.floating-element');

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        floatingElements.forEach((element, index) => {
            const speed = (index + 1) * 0.1;
            element.style.transform = `translateY(${rate * speed}px)`;
        });
    });
}

// 滚动到工具区域
function scrollToTools() {
    const toolsSection = document.getElementById('tools');
    if (toolsSection) {
        toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// 显示演示
function showDemo() {
    // 自动打开美化工具并加载示例
    openToolModal('beautify');
    setTimeout(() => {
        loadSample();
        setTimeout(() => {
            processHtml();
        }, 500);
    }, 300);
}

// 初始化滚动功能
function initializeScrollFeatures() {
    // 滚动进度指示器
    const scrollProgress = document.getElementById('scrollProgress');
    if (scrollProgress) {
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            scrollProgress.style.width = scrollPercent + '%';
        });
    }

    // 返回顶部按钮
    const backToTop = document.getElementById('backToTop');
    if (backToTop) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// 初始化粒子效果
function initializeParticleEffects() {
    const cards = document.querySelectorAll('.feature-card, .tool-card, .about-card');

    cards.forEach(card => {
        card.classList.add('particle-effect');

        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            card.style.setProperty('--mouse-x', x + '%');
            card.style.setProperty('--mouse-y', y + '%');
        });
    });
}

// 增强的字符统计功能
function updateCharacterCount() {
    const input = document.getElementById('inputHtml');
    const output = document.getElementById('outputHtml');

    if (!input || !output) return;

    const inputValue = input.value;
    const outputValue = output.value;

    // 更新输入字符统计
    updateCountDisplay('input', inputValue);

    // 更新输出字符统计
    if (outputValue) {
        updateCountDisplay('output', outputValue);
        showCompressionRatio(inputValue, outputValue);
    }
}

function updateCountDisplay(type, content) {
    const countElement = document.getElementById(`${type}-count`);
    if (countElement) {
        const chars = content.length;
        const lines = content.split('\n').length;
        const words = content.trim() ? content.trim().split(/\s+/).length : 0;

        countElement.innerHTML = `
            <span title="Characters">📝 ${chars.toLocaleString()}</span>
            <span title="Lines">📄 ${lines.toLocaleString()}</span>
            <span title="Words">📝 ${words.toLocaleString()}</span>
        `;
    }
}

function showCompressionRatio(input, output) {
    if (currentTool === 'compress' && input && output) {
        const originalSize = input.length;
        const compressedSize = output.length;
        const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

        if (ratio > 0) {
            showNotification(`Compression completed! Saved ${ratio}% space (${(originalSize - compressedSize).toLocaleString()} characters)`, 'success');
        }
    }
}



// 更新处理按钮文本
function updateProcessButton() {
    const processText = document.getElementById('processText');
    const toolNames = {
        'beautify': 'Beautify HTML',
        'compress': 'Minify HTML',
        'clean-links': 'Clean Links',
        'to-js': 'Convert to JS',
        'inline-css': 'Inline CSS'
    };
    processText.textContent = toolNames[currentTool];
}

// 加载示例HTML
function loadSample() {
    const sampleHtml = `<!DOCTYPE html>
<html>
<head>
    <title>Sample Page</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body { margin: 0; padding: 20px; }
        .container { max-width: 800px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to HTML Tools</h1>
        <p>This is a <a href="https://example.com" onclick="alert('clicked')">sample link</a></p>
        <div></div>
        <!-- This is a comment -->
        <p>   Extra   whitespace   </p>
    </div>
</body>
</html>`;

    document.getElementById('inputHtml').value = sampleHtml;
}

// 清空输入
function clearInput() {
    document.getElementById('inputHtml').value = '';
    document.getElementById('outputHtml').value = '';
}

// 处理HTML
function processHtml() {
    const input = document.getElementById('inputHtml').value.trim();
    if (!input) {
        showNotification('Please enter HTML code', 'warning');
        return;
    }

    // 显示处理中状态
    const processBtn = document.querySelector('.btn-primary');
    const originalText = processBtn.innerHTML;
    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    processBtn.disabled = true;

    // 使用setTimeout让UI有时间更新
    setTimeout(() => {
        try {
            let result = '';

            switch (currentTool) {
                case 'beautify':
                    result = beautifyHtml(input);
                    break;
                case 'compress':
                    result = compressHtml(input);
                    break;
                case 'clean-links':
                    result = cleanLinks(input);
                    break;
                case 'to-js':
                    result = convertToJs(input);
                    break;
                case 'inline-css':
                    result = inlineCss(input);
                    break;
            }

            document.getElementById('outputHtml').value = result;
            updateCharacterCount();
            showNotification('Processing completed!', 'success');

        } catch (error) {
            handleError(error, 'processHtml');
        } finally {
            // 恢复按钮状态
            processBtn.innerHTML = originalText;
            processBtn.disabled = false;
        }
    }, 100);
}

// HTML美化功能
function beautifyHtml(html) {
    try {
        const indentSize = document.getElementById('indent-size').checked ? 4 : 2;
        const fixTags = document.getElementById('fix-tags').checked;
        const removeEmpty = document.getElementById('remove-empty').checked;

        let result = html;
    
    // 修复未闭合标签
    if (fixTags) {
        const selfClosingTags = ['br', 'hr', 'img', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'];
        selfClosingTags.forEach(tag => {
            const regex = new RegExp(`<${tag}([^>]*?)(?<!/)>`, 'gi');
            result = result.replace(regex, `<${tag}$1 />`);
        });
    }
    
    // 移除空标签
    if (removeEmpty) {
        result = result.replace(/<(\w+)[^>]*>\s*<\/\1>/g, '');
    }
    
        // 格式化HTML
        result = formatHtml(result, indentSize);

        return result;
    } catch (error) {
        console.error('Error in beautifyHtml:', error);
        throw new Error('HTML beautification failed: ' + error.message);
    }
}

// HTML压缩功能
function compressHtml(html) {
    const removeComments = document.getElementById('remove-comments').checked;
    const removeWhitespace = document.getElementById('remove-whitespace').checked;
    const minifyInline = document.getElementById('minify-inline').checked;
    
    let result = html;
    
    // 移除注释
    if (removeComments) {
        result = result.replace(/<!--[\s\S]*?-->/g, '');
    }
    
    // 移除多余空白
    if (removeWhitespace) {
        result = result.replace(/\s+/g, ' ');
        result = result.replace(/>\s+</g, '><');
        result = result.trim();
    }
    
    // 压缩内联CSS和JS
    if (minifyInline) {
        // 压缩style标签内容
        result = result.replace(/<style[^>]*>([\s\S]*?)<\/style>/gi, function(match, css) {
            const minifiedCss = css.replace(/\s+/g, ' ').replace(/;\s*}/g, '}').replace(/{\s*/g, '{').trim();
            return match.replace(css, minifiedCss);
        });
        
        // 压缩script标签内容
        result = result.replace(/<script[^>]*>([\s\S]*?)<\/script>/gi, function(match, js) {
            const minifiedJs = js.replace(/\s+/g, ' ').replace(/;\s*}/g, ';}').replace(/{\s*/g, '{').trim();
            return match.replace(js, minifiedJs);
        });
    }
    
    return result;
}

// 清除链接功能
function cleanLinks(html) {
    const removeHref = document.getElementById('remove-href').checked;
    const removeOnclick = document.getElementById('remove-onclick').checked;
    const keepText = document.getElementById('keep-text').checked;
    
    let result = html;
    
    if (keepText) {
        // 保留链接文本，移除a标签
        result = result.replace(/<a[^>]*>(.*?)<\/a>/gi, '$1');
    } else {
        // 移除href属性
        if (removeHref) {
            result = result.replace(/href\s*=\s*["'][^"']*["']/gi, '');
        }
        
        // 移除onclick事件
        if (removeOnclick) {
            result = result.replace(/onclick\s*=\s*["'][^"']*["']/gi, '');
        }
    }
    
    return result;
}

// 转换为JS变量
function convertToJs(html) {
    const varName = document.getElementById('var-name').value || 'htmlContent';
    const useConst = document.getElementById('use-const').checked;
    const escapeQuotes = document.getElementById('escape-quotes').checked;
    
    let result = html;
    
    // 转义引号
    if (escapeQuotes) {
        result = result.replace(/\\/g, '\\\\');
        result = result.replace(/"/g, '\\"');
        result = result.replace(/'/g, "\\'");
    }
    
    // 转换为多行字符串
    const lines = result.split('\n');
    const jsLines = lines.map(line => `    "${line}"`);
    
    const declaration = useConst ? 'const' : 'var';
    const jsCode = `${declaration} ${varName} = [\n${jsLines.join(',\n')}\n].join('\\n');`;
    
    return jsCode;
}

// 内联CSS功能
function inlineCss(html) {
    const cssUrl = document.getElementById('css-url').value;
    const removeLinkTags = document.getElementById('remove-link-tags').checked;
    const minifyCss = document.getElementById('minify-css').checked;

    let result = html;

    if (cssUrl) {
        // 这里应该获取CSS内容并内联，但由于跨域限制，我们提供一个模拟实现
        showNotification('Due to browser security restrictions, external CSS files cannot be fetched directly. Please manually copy CSS content to style tags.', 'warning');
        return result;
    }

    // 移除link标签
    if (removeLinkTags) {
        result = result.replace(/<link[^>]*rel\s*=\s*["']stylesheet["'][^>]*>/gi, '');
    }

    return result;
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // ESC键关闭模态框
    if (e.key === 'Escape') {
        const modal = document.getElementById('toolModal');
        if (modal && modal.classList.contains('active')) {
            closeToolModal();
        }
    }

    // 在模态框打开时的快捷键
    const modal = document.getElementById('toolModal');
    if (modal && modal.classList.contains('active')) {
        // Ctrl+Enter 或 Cmd+Enter 处理HTML
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            processHtml();
        }

        // Ctrl+K 或 Cmd+K 清空输入
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            clearInput();
        }

        // Ctrl+D 或 Cmd+D 下载
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            downloadOutput();
        }

        // Ctrl+C 复制输出（当焦点不在输入框时）
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && !e.target.matches('textarea, input')) {
            e.preventDefault();
            copyOutput();
        }
    }
});

// 增强的拖拽功能
function initializeDragAndDrop() {
    const inputTextarea = document.getElementById('inputHtml');
    if (!inputTextarea) return;

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        inputTextarea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        inputTextarea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        inputTextarea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        inputTextarea.classList.add('drag-over');
    }

    function unhighlight(e) {
        inputTextarea.classList.remove('drag-over');
    }

    inputTextarea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];
            if (file.type === 'text/html' || file.name.endsWith('.html') || file.name.endsWith('.htm')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    inputTextarea.value = e.target.result;
                    updateCharacterCount();
                    showNotification('File loaded successfully', 'success');
                };
                reader.readAsText(file);
            } else {
                showNotification('Please upload an HTML file', 'warning');
            }
        }
    }
}

// HTML格式化函数
function formatHtml(html, indentSize) {
    // 确保indentSize是正数
    indentSize = Math.max(0, parseInt(indentSize) || 4);
    const indent = ' '.repeat(indentSize);
    let result = '';
    let level = 0;
    const tokens = html.match(/<\/?[^>]+>|[^<]+/g) || [];

    tokens.forEach(token => {
        if (token.match(/^<\/\w/)) {
            level = Math.max(0, level - 1); // 确保level不会小于0
            result += indent.repeat(level) + token + '\n';
        } else if (token.match(/^<\w[^>]*[^\/]>$/)) {
            result += indent.repeat(level) + token + '\n';
            level++;
        } else if (token.match(/^<\w[^>]*\/>$/)) {
            result += indent.repeat(level) + token + '\n';
        } else if (token.trim()) {
            result += indent.repeat(level) + token.trim() + '\n';
        }
    });

    return result.trim();
}

// 复制输出
function copyOutput() {
    const output = document.getElementById('outputHtml');
    if (!output.value.trim()) {
        showNotification('No content to copy', 'warning');
        return;
    }

    // 使用现代API或回退到旧方法
    if (navigator.clipboard) {
        navigator.clipboard.writeText(output.value).then(() => {
            showNotification('Content copied to clipboard', 'success');
            updateCopyButton();
        }).catch(() => {
            fallbackCopy(output);
        });
    } else {
        fallbackCopy(output);
    }
}

function fallbackCopy(output) {
    output.select();
    try {
        document.execCommand('copy');
        showNotification('Content copied to clipboard', 'success');
        updateCopyButton();
    } catch (err) {
        showNotification('Copy failed, please copy manually', 'error');
    }
}

function updateCopyButton() {
    const btn = event.target.closest('.btn');
    if (btn) {
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    }
}

// 下载输出
function downloadOutput() {
    const output = document.getElementById('outputHtml').value;
    if (!output) {
        showNotification('No content to download', 'warning');
        return;
    }

    const fileExtension = currentTool === 'to-js' ? 'js' : 'html';
    const blob = new Blob([output], { type: currentTool === 'to-js' ? 'text/javascript' : 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `processed_${currentTool}_${Date.now()}.${fileExtension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('File downloaded successfully', 'success');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas ${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 获取通知图标
function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

// 增强的错误处理
function handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    showNotification(`Processing error: ${error.message}`, 'error');
}

// 字符统计功能
function updateCharacterCount() {
    const input = document.getElementById('inputHtml').value;
    const output = document.getElementById('outputHtml').value;

    // 更新输入字符统计
    updateCountDisplay('input', input);

    // 更新输出字符统计
    if (output) {
        updateCountDisplay('output', output);
        showCompressionRatio(input, output);
    }
}

function updateCountDisplay(type, content) {
    const countElement = document.getElementById(`${type}-count`);
    if (countElement) {
        const chars = content.length;
        const lines = content.split('\n').length;
        const words = content.trim() ? content.trim().split(/\s+/).length : 0;

        countElement.innerHTML = `
            <span>字符: ${chars.toLocaleString()}</span>
            <span>行数: ${lines.toLocaleString()}</span>
            <span>单词: ${words.toLocaleString()}</span>
        `;
    }
}

function showCompressionRatio(input, output) {
    if (currentTool === 'compress' && input && output) {
        const originalSize = input.length;
        const compressedSize = output.length;
        const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

        showNotification(`Compression completed! Saved ${ratio}% space`, 'success');
    }
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+Enter 或 Cmd+Enter 处理HTML
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        processHtml();
    }

    // Ctrl+K 或 Cmd+K 清空输入
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearInput();
    }

    // Ctrl+D 或 Cmd+D 下载
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        downloadOutput();
    }
});


