<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vizulizer.com - Professional HTML Development Tools</title>
    <meta name="description" content="Professional HTML processing tools for developers. Beautify, compress, clean links, convert to JS variables, and inline CSS with enterprise-grade precision.">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 页面加载器 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-logo">
                <i class="fas fa-code"></i>
            </div>
            <div class="loader-text">Loading Vizulizer.com...</div>
        </div>
    </div>

    <!-- 滚动进度指示器 -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>
    <header class="header">
        <div class="container">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-code"></i>
                    <div class="logo-glow"></div>
                </div>
                <h1>Vizulizer.com</h1>
            </div>

            <nav class="nav" id="nav">
                <div class="nav-header">
                    <div class="nav-logo">
                        <i class="fas fa-code"></i>
                        <span>Vizulizer.com</span>
                    </div>
                    <button class="nav-close" id="navClose">    
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="nav-links">
                    <a href="index.html" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="about.html" class="nav-link">
                        <i class="fas fa-info-circle"></i>
                        <span>About Us </span>
                    </a>
                    <a href="privacy.html" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Privacy Policy</span>
                    </a>
                    <a href="terms.html" class="nav-link">
                        <i class="fas fa-file-contract"></i>
                        <span>Terms of Service</span>
                    </a>
                </div>
            </nav>

            <div class="header-controls">
                <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                    <i class="fas fa-sun" id="themeIcon"></i>
                    <div class="toggle-bg"></div>
                </button>

                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- 移动端导航遮罩 -->
    <div class="nav-overlay" id="navOverlay"></div>

    <main class="main">
        <div class="container">
            <section class="hero">
                <div class="hero-background">
                    <div class="floating-elements">
                        <div class="floating-element code-symbol" style="--delay: 0s; --duration: 20s;">&lt;/&gt;</div>
                        <div class="floating-element code-symbol" style="--delay: 5s; --duration: 25s;">{}</div>
                        <div class="floating-element code-symbol" style="--delay: 10s; --duration: 30s;">[]</div>
                        <div class="floating-element code-symbol" style="--delay: 15s; --duration: 35s;">&lt;html&gt;</div>
                        <div class="floating-element code-symbol" style="--delay: 8s; --duration: 28s;">CSS</div>
                        <div class="floating-element code-symbol" style="--delay: 12s; --duration: 32s;">JS</div>
                    </div>

                    <div class="hero-grid">
                        <div class="grid-line horizontal" style="--delay: 0s;"></div>
                        <div class="grid-line horizontal" style="--delay: 0.5s;"></div>
                        <div class="grid-line horizontal" style="--delay: 1s;"></div>
                        <div class="grid-line vertical" style="--delay: 1.5s;"></div>
                        <div class="grid-line vertical" style="--delay: 2s;"></div>
                    </div>

                    <div class="hero-particles">
                        <div class="particle" style="--delay: 0s; --x: 10%; --y: 20%;"></div>
                        <div class="particle" style="--delay: 2s; --x: 80%; --y: 30%;"></div>
                        <div class="particle" style="--delay: 4s; --x: 20%; --y: 70%;"></div>
                        <div class="particle" style="--delay: 6s; --x: 90%; --y: 60%;"></div>
                        <div class="particle" style="--delay: 8s; --x: 50%; --y: 80%;"></div>
                    </div>
                </div>

                <div class="hero-content">
                    <div class="hero-badge">
                        <i class="fas fa-rocket"></i>
                        <span>Professional Grade Tools</span>
                        <div class="badge-glow"></div>
                    </div>

                    <h2>
                        <span class="text-gradient">Next-Generation</span><br>
                        HTML Development Suite
                    </h2>
                    <p>Transform your HTML workflow with AI-powered tools designed for accuracy and reliability. All processing happens locally in your browser for maximum privacy and speed.</p>

                    <div class="hero-features">
                        <div class="feature-highlight">
                            <i class="fas fa-shield-alt"></i>
                            <span>100% Local Processing</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-bolt"></i>
                            <span>Lightning Fast</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-lock"></i>
                            <span>Privacy First</span>
                        </div>
                    </div>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="stat-number" data-target="50000">0</div>
                            <div class="stat-label">Lines Processed</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number" data-target="99">0</div>
                            <div class="stat-label">% Accuracy</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="stat-number" data-target="5">0</div>
                            <div class="stat-label">Tools Available</div>
                        </div>
                        
                    </div>

                    <div class="hero-actions">
                        <button class="btn btn-primary btn-hero btn-glow" onclick="scrollToTools()">
                            <i class="fas fa-play"></i>
                            <span>Start Processing</span>
                            <div class="btn-shine"></div>
                        </button>
                        <button class="btn btn-secondary btn-hero" onclick="showDemo()">
                            <i class="fas fa-eye"></i>
                            <span>View Demo</span>
                        </button>
                    </div>

                    <div class="hero-scroll-indicator">
                        <div class="scroll-arrow">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <span>Scroll to explore</span>
                    </div>
                </div>
            </section>

            <section class="features-overview">
                <div class="section-header">
                    <h3>Powerful Features</h3>
                    <p>Everything you need for professional HTML development</p>
                </div>

                <div class="features-grid">
                    <div class="feature-card" data-tool="beautify">
                        <div class="feature-icon">
                            <i class="fas fa-magic"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <h4>HTML Beautifier</h4>
                        <p>Automatically format and fix HTML syntax with intelligent indentation and structure optimization.</p>
                        <div class="feature-stats">
                            <span><i class="fas fa-zap"></i> Lightning Fast</span>
                            <span><i class="fas fa-check"></i> Auto-Fix</span>
                        </div>
                    </div>

                    <div class="feature-card" data-tool="compress">
                        <div class="feature-icon">
                            <i class="fas fa-compress-alt"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <h4>HTML Minifier</h4>
                        <p>Reduce file size by up to 70% while maintaining functionality and compatibility.</p>
                        <div class="feature-stats">
                            <span><i class="fas fa-chart-line"></i> 70% Reduction</span>
                            <span><i class="fas fa-shield-alt"></i> Safe</span>
                        </div>
                    </div>

                    <div class="feature-card" data-tool="clean-links">
                        <div class="feature-icon">
                            <i class="fas fa-unlink"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <h4>Link Cleaner</h4>
                        <p>Remove or sanitize links with precision control over what gets cleaned and what stays.</p>
                        <div class="feature-stats">
                            <span><i class="fas fa-sliders-h"></i> Configurable</span>
                            <span><i class="fas fa-broom"></i> Clean</span>
                        </div>
                    </div>

                    <div class="feature-card" data-tool="to-js">
                        <div class="feature-icon">
                            <i class="fab fa-js-square"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <h4>HTML to JS</h4>
                        <p>Convert HTML to JavaScript variables with proper escaping and formatting options.</p>
                        <div class="feature-stats">
                            <span><i class="fas fa-code"></i> Smart Escape</span>
                            <span><i class="fas fa-cogs"></i> Flexible</span>
                        </div>
                    </div>

                    <div class="feature-card" data-tool="inline-css">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                            <div class="icon-glow"></div>
                        </div>
                        <h4>CSS Inliner</h4>
                        <p>Inline CSS styles for better email compatibility and reduced HTTP requests.</p>
                        <div class="feature-stats">
                            <span><i class="fas fa-envelope"></i> Email Ready</span>
                            <span><i class="fas fa-tachometer-alt"></i> Fast Load</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How It Works Section -->
            <section class="how-it-works">
                <div class="section-header">
                    <h3>How It Works</h3>
                    <p>Simple, secure, and lightning-fast HTML processing in just three steps</p>
                </div>

                <div class="steps-container">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <h4>Input Your Code</h4>
                            <p>Paste your HTML code or drag & drop files directly into our editor. Supports all HTML formats and file sizes.</p>
                        </div>
                    </div>

                    <div class="step-connector"></div>

                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <h4>Choose Your Tool</h4>
                            <p>Select from our suite of professional tools and customize options to match your specific requirements.</p>
                        </div>
                    </div>

                    <div class="step-connector"></div>

                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <h4>Get Results</h4>
                            <p>Instantly receive your processed HTML with options to copy, download, or continue editing. All processing is local and secure.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Why Choose Us Section -->
            <section class="why-choose-us">
                <div class="section-header">
                    <h3>Why Choose Vizulizer.com?</h3>
                    <p>The most trusted HTML processing platform for developers worldwide</p>
                </div>

                <div class="benefits-grid">
                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-shield-check"></i>
                        </div>
                        <h4>100% Secure</h4>
                        <p>All processing happens locally in your browser. Your code never leaves your device, ensuring complete privacy and security.</p>
                        <div class="benefit-badge">Privacy First</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4>Lightning Fast</h4>
                        <p>Optimized algorithms deliver instant results regardless of file size. No waiting, no delays - just immediate processing.</p>
                        <div class="benefit-badge">Instant Results</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>Works Everywhere</h4>
                        <p>Fully responsive design works perfectly on desktop, tablet, and mobile. Access your tools anywhere, anytime.</p>
                        <div class="benefit-badge">Cross-Platform</div>
                    </div>

                    <div class="benefit-card">
                        <div class="benefit-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4>Professional Grade</h4>
                        <p>Enterprise-level tools trusted by developers at top companies. Reliable, accurate, and feature-rich processing.</p>
                        <div class="benefit-badge">Enterprise Ready</div>
                    </div>
                </div>
            </section>

            <section class="tools-section" id="tools">
                <div class="section-header">
                    <h3>Choose Your Tool</h3>
                    <p>Select a tool to start processing your HTML code</p>
                </div>

                <div class="tools-grid">
                    <div class="tool-card" data-tool="beautify">
                        <div class="tool-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h4>HTML Beautifier</h4>
                        <p>Format and beautify HTML code</p>
                        <button class="tool-btn">
                            <span>Open Tool</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <div class="tool-card" data-tool="compress">
                        <div class="tool-icon">
                            <i class="fas fa-compress-alt"></i>
                        </div>
                        <h4>HTML Minifier</h4>
                        <p>Compress and minify HTML</p>
                        <button class="tool-btn">
                            <span>Open Tool</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <div class="tool-card" data-tool="clean-links">
                        <div class="tool-icon">
                            <i class="fas fa-unlink"></i>
                        </div>
                        <h4>Link Cleaner</h4>
                        <p>Remove or clean HTML links</p>
                        <button class="tool-btn">
                            <span>Open Tool</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <div class="tool-card" data-tool="to-js">
                        <div class="tool-icon">
                            <i class="fab fa-js-square"></i>
                        </div>
                        <h4>HTML to JS</h4>
                        <p>Convert HTML to JavaScript</p>
                        <button class="tool-btn">
                            <span>Open Tool</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>

                    <div class="tool-card" data-tool="inline-css">
                        <div class="tool-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h4>CSS Inliner</h4>
                        <p>Inline CSS into HTML</p>
                        <button class="tool-btn">
                            <span>Open Tool</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Tool Modal -->
            <div class="tool-modal" id="toolModal">
                <div class="modal-backdrop" onclick="closeToolModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modalTitle">HTML Tool</h3>
                        <button class="modal-close" onclick="closeToolModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="input-section">
                            <div class="input-header">
                                <h4>Input HTML Code</h4>
                                <div class="input-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="clearInput()">
                                        <i class="fas fa-trash"></i> Clear
                                    </button>
                                    <button class="btn btn-secondary btn-sm" onclick="loadSample()">
                                        <i class="fas fa-file-code"></i> Sample
                                    </button>
                                </div>
                            </div>
                            <textarea id="inputHtml" placeholder="Enter or paste your HTML code here... Supports drag & drop HTML files"></textarea>
                            <div class="char-count" id="input-count"></div>
                        </div>

                        <div class="process-section">
                            <button class="btn btn-primary" onclick="processHtml()">
                                <i class="fas fa-play"></i>
                                <span id="processText">Process HTML</span>
                            </button>
                        </div>

                        <div class="output-section">
                            <div class="output-header">
                                <h4>Processed Result</h4>
                                <div class="output-actions">
                                    <button class="btn btn-secondary btn-sm" onclick="copyOutput()">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                    <button class="btn btn-secondary btn-sm" onclick="downloadOutput()">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                </div>
                            </div>
                            <textarea id="outputHtml" readonly placeholder="Processed result will appear here..."></textarea>
                            <div class="char-count" id="output-count"></div>
                        </div>

                        <!-- Tool-specific options -->
                        <div class="tool-options">
                            <div id="beautify-options" class="options-panel active">
                                <h4>Beautifier Options</h4>
                                <label><input type="checkbox" id="indent-size" checked> Use 4-space indentation</label>
                                <label><input type="checkbox" id="fix-tags" checked> Fix unclosed tags</label>
                                <label><input type="checkbox" id="remove-empty" checked> Remove empty tags</label>
                            </div>

                            <div id="compress-options" class="options-panel">
                                <h4>Minifier Options</h4>
                                <label><input type="checkbox" id="remove-comments" checked> Remove comments</label>
                                <label><input type="checkbox" id="remove-whitespace" checked> Remove extra whitespace</label>
                                <label><input type="checkbox" id="minify-inline" checked> Minify inline CSS/JS</label>
                            </div>

                            <div id="clean-links-options" class="options-panel">
                                <h4>Link Cleaner Options</h4>
                                <label><input type="checkbox" id="remove-href" checked> Remove href attributes</label>
                                <label><input type="checkbox" id="remove-onclick" checked> Remove onclick events</label>
                                <label><input type="checkbox" id="keep-text" checked> Keep link text</label>
                            </div>

                            <div id="to-js-options" class="options-panel">
                                <h4>HTML to JS Options</h4>
                                <label>Variable name: <input type="text" id="var-name" value="htmlContent" placeholder="Variable name"></label>
                                <label><input type="checkbox" id="use-const" checked> Use const declaration</label>
                                <label><input type="checkbox" id="escape-quotes" checked> Escape quotes</label>
                            </div>

                            <div id="inline-css-options" class="options-panel">
                                <h4>CSS Inliner Options</h4>
                                <label>CSS file URL: <input type="text" id="css-url" placeholder="Enter CSS file URL"></label>
                                <label><input type="checkbox" id="remove-link-tags" checked> Remove link tags</label>
                                <label><input type="checkbox" id="minify-css" checked> Minify CSS</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-code"></i>
                            <div class="logo-glow"></div>
                        </div>
                        <h3>Vizulizer.com</h3>
                    </div>
                    <p>Professional HTML development tools designed for accuracy and reliability. All processing happens locally in your browser for maximum privacy and speed.</p>

                    <div class="social-links">
                        <a href="https://facebook.com/Vizulizer.com" class="social-link" title="Facebook" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com/Vizulizer.com" class="social-link" title="Twitter" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://linkedin.com/company/Vizulizer.com" class="social-link" title="LinkedIn" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="https://github.com/Vizulizer.com" class="social-link" title="GitHub" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://discord.gg/Vizulizer.com" class="social-link" title="Discord" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-discord"></i>
                        </a>
                    </div>
                </div>

                <div class="footer-links-section">
                    <h4>Tools Links</h4>
                    <div class="footer-links">
                        <a href="#" onclick="openToolModal('beautify')">HTML Beautifier</a>
                        <a href="#" onclick="openToolModal('compress')">HTML Minifier</a>
                        <a href="#" onclick="openToolModal('clean-links')">Link Cleaner</a>
                        <a href="#" onclick="openToolModal('to-js')">HTML to JS</a>
                        <a href="#" onclick="openToolModal('inline-css')">CSS Inliner</a>
                    </div>
                </div>

                <div class="footer-links-section">
                    <h4>Quick Links</h4>
                    <div class="footer-links">
                        <a href="index.html">Home</a>
                        <a href="about.html">About Us</a>
                        <a href="privacy.html">Privacy Policy</a>
                        <a href="terms.html">Terms of Service</a>
                    </div>
                </div>
            </div>

            <div class="footer-divider"></div>

            < <div class="footer-bottom">
                <p>&copy; 2025 vizulizer.com. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop" title="Back to Top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script src="scripts/main.js"></script>
</body>
</html>
