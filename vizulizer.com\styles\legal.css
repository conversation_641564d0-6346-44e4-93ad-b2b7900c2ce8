/* 法律页面（Privacy和Terms）共用样式 - 深色主题 */

.legal-content {
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(30, 41, 59, 0.6);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 20px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.legal-header {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    padding: 4rem 2.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.legal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    z-index: 1;
}

.legal-header h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    position: relative;
    z-index: 2;
}

.last-updated {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 400;
    position: relative;
    z-index: 2;
}

.legal-body {
    padding: 3.5rem;
}

.legal-section {
    margin-bottom: 3.5rem;
}

.legal-section:last-child {
    margin-bottom: 0;
}

.legal-section h2 {
    font-size: 2rem;
    color: #f8fafc;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid;
    border-image: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) 1;
    font-weight: 700;
}

.legal-section h3 {
    font-size: 1.5rem;
    color: #cbd5e1;
    margin: 2.5rem 0 1.25rem 0;
    font-weight: 600;
}

.legal-section p {
    color: #94a3b8;
    line-height: 1.8;
    margin-bottom: 1.75rem;
    font-size: 1.05rem;
}

.legal-section ul {
    margin: 1.5rem 0;
    padding-left: 0;
}

.legal-section li {
    color: #94a3b8;
    line-height: 1.8;
    margin-bottom: 1rem;
    padding-left: 2.5rem;
    position: relative;
    list-style: none;
}

.legal-section li::before {
    content: "•";
    color: #3b82f6;
    font-size: 1.3rem;
    position: absolute;
    left: 0.75rem;
    top: 0;
    font-weight: bold;
}

.legal-section strong {
    color: #f8fafc;
    font-weight: 600;
}

/* 高亮框样式 - 深色主题 */
.highlight-box {
    background: rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-left: 4px solid #3b82f6;
    padding: 2.5rem;
    margin: 2.5rem 0;
    border-radius: 0 12px 12px 0;
    position: relative;
    overflow: hidden;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    z-index: -1;
}

.highlight-box h3 {
    color: #60a5fa;
    margin-top: 0;
    margin-bottom: 1.25rem;
    font-size: 1.4rem;
    font-weight: 600;
}

.highlight-box p {
    margin-bottom: 0;
    color: #cbd5e1;
    font-size: 1.05rem;
}

/* 联系信息样式 */
.contact-info {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    margin-top: 1.5rem;
}

.contact-info p {
    margin-bottom: 1rem;
    color: #495057;
}

.contact-info p:last-child {
    margin-bottom: 0;
}

.contact-info a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.contact-info a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 链接样式 */
.legal-section a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.legal-section a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 编号列表样式 */
.legal-section ol {
    counter-reset: item;
    padding-left: 0;
}

.legal-section ol li {
    display: block;
    margin-bottom: 1rem;
    padding-left: 2.5rem;
    position: relative;
}

.legal-section ol li::before {
    content: counter(item) ".";
    counter-increment: item;
    color: #667eea;
    font-weight: 600;
    position: absolute;
    left: 0;
    top: 0;
}

/* 表格样式（如果需要） */
.legal-table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.legal-table th,
.legal-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.legal-table th {
    background: #667eea;
    color: white;
    font-weight: 600;
}

.legal-table tr:hover {
    background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .legal-content {
        margin: 1rem;
        border-radius: 10px;
    }
    
    .legal-header {
        padding: 2rem 1.5rem;
    }
    
    .legal-header h1 {
        font-size: 2rem;
    }
    
    .legal-body {
        padding: 2rem 1.5rem;
    }
    
    .legal-section h2 {
        font-size: 1.5rem;
    }
    
    .legal-section h3 {
        font-size: 1.2rem;
    }
    
    .highlight-box {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }
    
    .contact-info {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .legal-content {
        margin: 0.5rem;
    }
    
    .legal-header {
        padding: 1.5rem 1rem;
    }
    
    .legal-header h1 {
        font-size: 1.8rem;
    }
    
    .legal-body {
        padding: 1.5rem 1rem;
    }
    
    .legal-section {
        margin-bottom: 2rem;
    }
    
    .legal-section h2 {
        font-size: 1.3rem;
    }
    
    .legal-section h3 {
        font-size: 1.1rem;
    }
    
    .legal-section li {
        padding-left: 1.5rem;
    }
    
    .legal-section ol li {
        padding-left: 2rem;
    }
    
    .highlight-box {
        padding: 1rem;
    }
    
    .contact-info {
        padding: 1rem;
    }
}

/* 打印样式 */
@media print {
    .legal-content {
        box-shadow: none;
        border-radius: 0;
    }
    
    .legal-header {
        background: none !important;
        color: black !important;
    }
    
    .highlight-box {
        background: none !important;
        border: 1px solid #ccc;
    }
    
    .legal-section a {
        color: black !important;
        text-decoration: underline;
    }
}
