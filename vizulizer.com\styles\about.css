/* About页面专用样式 - 深色主题 */

.about-hero {
    text-align: center;
    margin-bottom: 4rem;
    padding: 4rem 0;
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.about-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    z-index: -1;
}

.about-hero h2 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #3b82f6 50%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    line-height: 1.2;
}

.about-hero p {
    font-size: 1.4rem;
    color: #94a3b8;
    max-width: 800px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.7;
}

.about-content {
    margin-bottom: 4rem;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.about-card {
    background: rgba(30, 41, 59, 0.6);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.about-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.about-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    border-color: rgba(59, 130, 246, 0.3);
}

.about-card:hover::before {
    opacity: 1;
}

.card-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.card-icon i {
    font-size: 2.2rem;
    color: white;
}

.about-card h3 {
    font-size: 1.6rem;
    margin-bottom: 1.5rem;
    color: #f8fafc;
    font-weight: 700;
}

.about-card p {
    color: #cbd5e1;
    line-height: 1.8;
    font-size: 1rem;
}

.features-section {
    margin-bottom: 4rem;
}

.features-section h3 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 3.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
}

.feature-item {
    background: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(148, 163, 184, 0.1);
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.feature-item:hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-item:hover::before {
    opacity: 1;
}

.feature-item i {
    font-size: 3rem;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
}

.feature-item h4 {
    font-size: 1.3rem;
    margin-bottom: 1.25rem;
    color: #f8fafc;
    font-weight: 600;
}

.feature-item p {
    color: #cbd5e1;
    font-size: 0.95rem;
    line-height: 1.7;
}

.contact-section {
    margin-bottom: 4rem;
}

.contact-section h3 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s;
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-icon i {
    font-size: 1.8rem;
    color: white;
}

.contact-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.contact-card p {
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.contact-link {
    display: inline-block;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border: 2px solid #667eea;
    border-radius: 25px;
    transition: all 0.3s;
}

.contact-link:hover {
    background-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

.company-info {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.info-item h4 {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.info-item p {
    margin-bottom: 0.8rem;
    color: #555;
    line-height: 1.6;
}

.info-item strong {
    color: #2c3e50;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .about-hero h2 {
        font-size: 2.2rem;
    }
    
    .about-hero p {
        font-size: 1.1rem;
    }
    
    .about-grid,
    .contact-grid,
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .about-card,
    .contact-card {
        padding: 2rem;
    }
    
    .company-info {
        padding: 2rem;
    }
    
    .features-section h3,
    .contact-section h3 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .about-hero {
        padding: 2rem 0;
    }
    
    .about-hero h2 {
        font-size: 1.8rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .about-card,
    .contact-card {
        padding: 1.5rem;
    }
    
    .card-icon,
    .contact-icon {
        width: 60px;
        height: 60px;
    }
    
    .card-icon i,
    .contact-icon i {
        font-size: 1.5rem;
    }
}
